@echo off
echo Game Images Batch Downloader
echo ===========================
echo.
echo This script will help you download images for each game.
echo Press any key to continue or Ctrl+C to exit.
pause
echo.


echo Processing: Fortnite
echo Currency: V-Bucks
echo.
echo Required files:
echo 1. Fortnite\img\gameicon.png (512x512)
echo 2. Fortnite\img\currency.png (256x256)  
echo 3. Fortnite\img\wallpaper.jpg (1920x1080)
echo.
echo Search suggestions:
echo - "Fortnite game icon 512x512"
echo - "Fortnite V-Bucks icon"
echo - "Fortnite game wallpaper 1920x1080"
echo.
pause

echo Processing: PUBG Mobile
echo Currency: UC
echo.
echo Required files:
echo 1. PUBG Mobile\img\gameicon.png (512x512)
echo 2. PUBG Mobile\img\currency.png (256x256)  
echo 3. PUBG Mobile\img\wallpaper.jpg (1920x1080)
echo.
echo Search suggestions:
echo - "PUBG Mobile game icon 512x512"
echo - "PUBG Mobile UC icon"
echo - "PUBG Mobile game wallpaper 1920x1080"
echo.
pause

echo Processing: Free Fire
echo Currency: Diamonds
echo.
echo Required files:
echo 1. Free Fire\img\gameicon.png (512x512)
echo 2. Free Fire\img\currency.png (256x256)  
echo 3. Free Fire\img\wallpaper.jpg (1920x1080)
echo.
echo Search suggestions:
echo - "Free Fire game icon 512x512"
echo - "Free Fire Diamonds icon"
echo - "Free Fire game wallpaper 1920x1080"
echo.
pause

echo Processing: Roblox
echo Currency: Robux
echo.
echo Required files:
echo 1. Roblox\img\gameicon.png (512x512)
echo 2. Roblox\img\currency.png (256x256)  
echo 3. Roblox\img\wallpaper.jpg (1920x1080)
echo.
echo Search suggestions:
echo - "Roblox game icon 512x512"
echo - "Roblox Robux icon"
echo - "Roblox game wallpaper 1920x1080"
echo.
pause

echo Processing: Clash of Clans
echo Currency: Gems
echo.
echo Required files:
echo 1. Clash of Clans\img\gameicon.png (512x512)
echo 2. Clash of Clans\img\currency.png (256x256)  
echo 3. Clash of Clans\img\wallpaper.jpg (1920x1080)
echo.
echo Search suggestions:
echo - "Clash of Clans game icon 512x512"
echo - "Clash of Clans Gems icon"
echo - "Clash of Clans game wallpaper 1920x1080"
echo.
pause

echo Processing: Candy Crush Saga
echo Currency: Gold Bars
echo.
echo Required files:
echo 1. Candy Crush Saga\img\gameicon.png (512x512)
echo 2. Candy Crush Saga\img\currency.png (256x256)  
echo 3. Candy Crush Saga\img\wallpaper.jpg (1920x1080)
echo.
echo Search suggestions:
echo - "Candy Crush Saga game icon 512x512"
echo - "Candy Crush Saga Gold Bars icon"
echo - "Candy Crush Saga game wallpaper 1920x1080"
echo.
pause

echo Processing: Pokemon Go
echo Currency: PokéCoins
echo.
echo Required files:
echo 1. Pokemon Go\img\gameicon.png (512x512)
echo 2. Pokemon Go\img\currency.png (256x256)  
echo 3. Pokemon Go\img\wallpaper.jpg (1920x1080)
echo.
echo Search suggestions:
echo - "Pokemon Go game icon 512x512"
echo - "Pokemon Go PokéCoins icon"
echo - "Pokemon Go game wallpaper 1920x1080"
echo.
pause

echo Processing: Subway Surfers
echo Currency: Coins
echo.
echo Required files:
echo 1. Subway Surfers\img\gameicon.png (512x512)
echo 2. Subway Surfers\img\currency.png (256x256)  
echo 3. Subway Surfers\img\wallpaper.jpg (1920x1080)
echo.
echo Search suggestions:
echo - "Subway Surfers game icon 512x512"
echo - "Subway Surfers Coins icon"
echo - "Subway Surfers game wallpaper 1920x1080"
echo.
pause

echo Processing: COD Mobile
echo Currency: CP
echo.
echo Required files:
echo 1. COD Mobile\img\gameicon.png (512x512)
echo 2. COD Mobile\img\currency.png (256x256)  
echo 3. COD Mobile\img\wallpaper.jpg (1920x1080)
echo.
echo Search suggestions:
echo - "COD Mobile game icon 512x512"
echo - "COD Mobile CP icon"
echo - "COD Mobile game wallpaper 1920x1080"
echo.
pause

echo Processing: Brawl Stars
echo Currency: Gems
echo.
echo Required files:
echo 1. Brawl Stars\img\gameicon.png (512x512)
echo 2. Brawl Stars\img\currency.png (256x256)  
echo 3. Brawl Stars\img\wallpaper.jpg (1920x1080)
echo.
echo Search suggestions:
echo - "Brawl Stars game icon 512x512"
echo - "Brawl Stars Gems icon"
echo - "Brawl Stars game wallpaper 1920x1080"
echo.
pause
