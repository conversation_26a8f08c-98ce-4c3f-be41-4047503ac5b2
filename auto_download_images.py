#!/usr/bin/env python3
"""
Advanced Game Images Auto Downloader
Downloads gameicon, currency, and wallpaper for each game
"""

import os
import requests
import time
import json
from urllib.parse import quote, urljoin
from bs4 import BeautifulSoup
import re

# Game list with currency information
games_data = {
    "Fortnite": {"currency": "V-Bucks", "type": "Battle Royale"},
    "PUBG Mobile": {"currency": "UC", "type": "Battle Royale"},
    "Free Fire": {"currency": "Diamonds", "type": "Battle Royale"},
    "DLS 25": {"currency": "Coins", "type": "Sports"},
    "eFootball": {"currency": "eFootball Coins", "type": "Sports"},
    "FC MOBILE": {"currency": "FC Points", "type": "Sports"},
    "Roblox": {"currency": "Robux", "type": "Platform"},
    "Monopoly GO": {"currency": "Dice Rolls", "type": "Board Game"},
    "Stumble Guys": {"currency": "Gems", "type": "Party Game"},
    "Brawl Stars": {"currency": "Gems", "type": "MOBA"},
    "Royal Match": {"currency": "Coins", "type": "Puzzle"},
    "Lords Mobile": {"currency": "Gems", "type": "Strategy"},
    "Township": {"currency": "Coins", "type": "City Builder"},
    "Bingo Blitz": {"currency": "Credits", "type": "Casino"},
    "Homescapes": {"currency": "Coins", "type": "Puzzle"},
    "Blood Strike": {"currency": "Diamonds", "type": "FPS"},
    "BASEBALL 9": {"currency": "Coins", "type": "Sports"},
    "Match Master": {"currency": "Coins", "type": "Puzzle"},
    "Dragon City": {"currency": "Gems", "type": "Strategy"},
    "Candy Crush Saga": {"currency": "Gold Bars", "type": "Puzzle"},
    "Mech Arena": {"currency": "A-Coins", "type": "Action"},
    "Cooking Fever": {"currency": "Gems", "type": "Simulation"},
    "Board Kings": {"currency": "Rolls", "type": "Board Game"},
    "ZEPETO": {"currency": "Coins", "type": "Social"},
    "DRAGON BALL LEGENDS": {"currency": "Chrono Crystals", "type": "RPG"},
    "ONE PIECE": {"currency": "Rainbow Diamonds", "type": "RPG"},
    "Subway Surfers": {"currency": "Coins", "type": "Endless Runner"},
    "Clash of Clans": {"currency": "Gems", "type": "Strategy"},
    "8 Ball Pool": {"currency": "Coins", "type": "Sports"},
    "Pokemon Go": {"currency": "PokéCoins", "type": "AR Game"},
    "Car Parking": {"currency": "Money", "type": "Simulation"},
    "Car Parking Multiplayer 2": {"currency": "Money", "type": "Simulation"},
    "Chikii": {"currency": "Coins", "type": "Cloud Gaming"},
    "TopTop": {"currency": "Coins", "type": "Social"},
    "WePlay": {"currency": "Coins", "type": "Social"},
    "Jawaker": {"currency": "Chips", "type": "Card Game"},
    "Hay Day": {"currency": "Diamonds", "type": "Farming"},
    "FROZEN CASH": {"currency": "Cash", "type": "Casino"},
    "Yalla Ludo": {"currency": "Diamonds", "type": "Board Game"},
    "Ludo Star": {"currency": "Coins", "type": "Board Game"},
    "Ludo Club": {"currency": "Coins", "type": "Board Game"},
    "Blockman Go": {"currency": "Cubes", "type": "Platform"},
    "CarX Street": {"currency": "Credits", "type": "Racing"},
    "Capybara Go": {"currency": "Coins", "type": "Adventure"},
    "PK XD": {"currency": "Gems", "type": "Social"},
    "Zooba": {"currency": "Gems", "type": "Battle Royale"},
    "Family Island": {"currency": "Rubies", "type": "Adventure"},
    "Coin Master": {"currency": "Spins", "type": "Casino"},
    "COD Mobile": {"currency": "CP", "type": "FPS"},
    "Blooket": {"currency": "Tokens", "type": "Educational"},
    "Hero Wars": {"currency": "Emeralds", "type": "RPG"},
    "Hide Online": {"currency": "Coins", "type": "Action"},
    "HeadBall 2": {"currency": "Diamonds", "type": "Sports"},
    "World of Tanks Blitz": {"currency": "Gold", "type": "Action"},
    "Uno": {"currency": "Coins", "type": "Card Game"},
    "BitLife": {"currency": "Bitizenship", "type": "Simulation"},
    "Avakin Life": {"currency": "Avacoins", "type": "Social"},
    "Tango": {"currency": "Coins", "type": "Social"},
    "HalaMe": {"currency": "Coins", "type": "Social"},
    "Sugo": {"currency": "Coins", "type": "Social"},
    "Bigo Live": {"currency": "Diamonds", "type": "Live Streaming"},
    "Poppo Live": {"currency": "Coins", "type": "Live Streaming"},
    "Tigo": {"currency": "Coins", "type": "Social"},
    "Cafe": {"currency": "Coins", "type": "Social"},
    "Starchat": {"currency": "Coins", "type": "Social"},
    "Soulchill": {"currency": "Diamonds", "type": "Social"},
    "Chamet": {"currency": "Diamonds", "type": "Social"},
    "Azar": {"currency": "Gems", "type": "Social"},
    "Top Follow": {"currency": "Coins", "type": "Social"},
    "Timo": {"currency": "Coins", "type": "Social"},
    "Golf Battle": {"currency": "Gems", "type": "Sports"},
    "Last War Survival": {"currency": "Diamonds", "type": "Strategy"},
    "SimCity BuildIt": {"currency": "SimCash", "type": "City Builder"},
    "NBA Infinite": {"currency": "Coins", "type": "Sports"},
    "FarmVille 2": {"currency": "Farm Bucks", "type": "Farming"}
}

def download_image(url, filepath, headers=None):
    """Download an image from URL to filepath"""
    try:
        if headers is None:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        
        response = requests.get(url, headers=headers, timeout=30, stream=True)
        response.raise_for_status()
        
        # Check if it's actually an image
        content_type = response.headers.get('content-type', '')
        if not content_type.startswith('image/'):
            print(f"Warning: {url} is not an image (content-type: {content_type})")
            return False
        
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"✅ Downloaded: {os.path.basename(filepath)}")
        return True
    except Exception as e:
        print(f"❌ Error downloading {url}: {e}")
        return False

def search_google_images(query, size_filter=""):
    """Search Google Images and return image URLs"""
    try:
        search_url = f"https://www.google.com/search?q={quote(query)}&tbm=isch"
        if size_filter:
            search_url += f"&tbs=isz:ex,iszw:{size_filter.split('x')[0]},iszh:{size_filter.split('x')[1]}"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(search_url, headers=headers)
        response.raise_for_status()
        
        # Extract image URLs from the response
        img_urls = re.findall(r'"ou":"([^"]+)"', response.text)
        return img_urls[:5]  # Return first 5 URLs
    except Exception as e:
        print(f"Error searching for {query}: {e}")
        return []

def create_download_urls_file():
    """Create a comprehensive file with download URLs for manual use"""
    urls_data = {}
    
    for game_name, game_info in games_data.items():
        currency = game_info['currency']
        game_type = game_info['type']
        
        urls_data[game_name] = {
            "info": game_info,
            "search_queries": {
                "gameicon": f"{game_name} game icon logo 512x512",
                "currency": f"{game_name} {currency} icon currency",
                "wallpaper": f"{game_name} game wallpaper 1920x1080"
            },
            "google_search_urls": {
                "gameicon": f"https://www.google.com/search?q={quote(game_name + ' game icon logo 512x512')}&tbm=isch&tbs=isz:ex,iszw:512,iszh:512",
                "currency": f"https://www.google.com/search?q={quote(game_name + ' ' + currency + ' icon currency')}&tbm=isch",
                "wallpaper": f"https://www.google.com/search?q={quote(game_name + ' game wallpaper 1920x1080')}&tbm=isch&tbs=isz:ex,iszw:1920,iszh:1080"
            },
            "file_paths": {
                "gameicon": f"{game_name}/img/gameicon.png",
                "currency": f"{game_name}/img/currency.png", 
                "wallpaper": f"{game_name}/img/wallpaper.jpg"
            }
        }
    
    with open("download_urls.json", "w", encoding="utf-8") as f:
        json.dump(urls_data, f, indent=2, ensure_ascii=False)
    
    print("✅ Created download_urls.json with all search URLs")

def attempt_auto_download():
    """Attempt to automatically download images"""
    print("🚀 Starting automatic download process...")
    success_count = 0
    total_attempts = 0
    
    for game_name, game_info in list(games_data.items())[:5]:  # Test with first 5 games
        print(f"\n📱 Processing: {game_name}")
        game_folder = os.path.join(game_name, "img")
        
        if not os.path.exists(game_folder):
            print(f"❌ Folder not found: {game_folder}")
            continue
        
        currency = game_info['currency']
        
        # Try to download gameicon
        icon_query = f"{game_name} game icon logo"
        icon_urls = search_google_images(icon_query, "512x512")
        
        for url in icon_urls[:3]:  # Try first 3 URLs
            icon_path = os.path.join(game_folder, "gameicon.png")
            total_attempts += 1
            if download_image(url, icon_path):
                success_count += 1
                break
            time.sleep(1)
        
        # Try to download currency icon
        currency_query = f"{game_name} {currency} icon"
        currency_urls = search_google_images(currency_query)
        
        for url in currency_urls[:3]:
            currency_path = os.path.join(game_folder, "currency.png")
            total_attempts += 1
            if download_image(url, currency_path):
                success_count += 1
                break
            time.sleep(1)
        
        # Try to download wallpaper
        wallpaper_query = f"{game_name} game wallpaper"
        wallpaper_urls = search_google_images(wallpaper_query, "1920x1080")
        
        for url in wallpaper_urls[:3]:
            wallpaper_path = os.path.join(game_folder, "wallpaper.jpg")
            total_attempts += 1
            if download_image(url, wallpaper_path):
                success_count += 1
                break
            time.sleep(1)
        
        time.sleep(2)  # Delay between games
    
    print(f"\n📊 Download Summary:")
    print(f"✅ Successful downloads: {success_count}")
    print(f"📝 Total attempts: {total_attempts}")
    print(f"📈 Success rate: {(success_count/total_attempts*100):.1f}%" if total_attempts > 0 else "No attempts made")

def main():
    print("🎮 Advanced Game Images Auto Downloader")
    print("=" * 50)
    
    # Create URLs file for manual download
    create_download_urls_file()
    
    # Ask user if they want to attempt auto download
    print("\nOptions:")
    print("1. Create download URLs file only (RECOMMENDED)")
    print("2. Attempt automatic download (EXPERIMENTAL)")
    
    choice = input("\nEnter your choice (1 or 2): ").strip()
    
    if choice == "2":
        print("\n⚠️  WARNING: Automatic download is experimental and may not work reliably.")
        print("Google has anti-bot measures that may block requests.")
        confirm = input("Do you want to continue? (y/N): ").strip().lower()
        
        if confirm == 'y':
            attempt_auto_download()
        else:
            print("Cancelled automatic download.")
    
    print("\n✅ Process completed!")
    print("📁 Check download_urls.json for manual download links")
    print("🌐 Use the HTML helper for easier manual downloading")

if __name__ == "__main__":
    main()
