# دليل إنشاء مجلدات الألعاب وتحميل الصور

## نظرة عامة
هذا المشروع يساعدك في إنشاء هيكل مجلدات منظم لجميع الألعاب مع تحميل الصور المناسبة لكل لعبة.

## الملفات المتوفرة

### 1. `create_game_folders.ps1`
سكريپت PowerShell لإنشاء جميع مجلدات الألعاب تلقائياً.

**كيفية الاستخدام:**
```powershell
# في PowerShell
.\create_game_folders.ps1
```

**ما يفعله:**
- ينشئ مجلد لكل لعبة من القائمة
- ينشئ مجلد فرعي `img` داخل كل مجلد لعبة
- ينشئ ملف ملخص `games_structure_summary.txt`

### 2. `download_game_images.py`
سكريپت Python لمساعدتك في تحميل صور الألعاب.

**كيفية الاستخدام:**
```bash
python download_game_images.py
```

**ما يفعله:**
- ينشئ ملف `games_info.json` مع معلومات جميع الألعاب
- ينشئ ملف `manual_download_helper.bat` للمساعدة في التحميل اليدوي

## الخطوات المقترحة

### الخطوة 1: إنشاء المجلدات
```powershell
.\create_game_folders.ps1
```

### الخطوة 2: تحضير معلومات التحميل
```bash
python download_game_images.py
```

### الخطوة 3: تحميل الصور
لديك عدة خيارات:

#### أ) التحميل اليدوي (الأسرع والأدق)
1. شغل `manual_download_helper.bat`
2. لكل لعبة، ابحث عن:
   - أيقونة اللعبة (512x512 أو 1024x1024 بكسل)
   - صورة غلاف اللعبة (1920x1080 أو مشابه)
3. احفظ الصور في المكان المحدد

#### ب) التحميل شبه التلقائي
استخدم مواقع مثل:
- [Google Images](https://images.google.com)
- [Game Icons](https://www.gameicons.net)
- [Steam Store](https://store.steampowered.com) للألعاب المتوفرة
- [App Store](https://apps.apple.com) أو [Google Play](https://play.google.com) للألعاب المحمولة

## هيكل المجلدات النهائي
```
All games/
├── Fortnite/
│   └── img/
│       ├── icon.png
│       └── cover.jpg
├── PUBG Mobile/
│   └── img/
│       ├── icon.png
│       └── cover.jpg
├── Free Fire/
│   └── img/
│       ├── icon.png
│       └── cover.jpg
└── ... (باقي الألعاب)
```

## نصائح للحصول على صور عالية الجودة

### للأيقونات:
- ابحث عن صور بدقة 512x512 أو 1024x1024 بكسل
- تأكد من أن الخلفية شفافة (PNG)
- استخدم الأيقونات الرسمية من متاجر التطبيقات

### لصور الغلاف:
- ابحث عن صور بدقة 1920x1080 أو أعلى
- استخدم الصور الترويجية الرسمية
- تجنب الصور ذات العلامات المائية

## مواقع مفيدة للصور

### أيقونات الألعاب:
- [App Store](https://apps.apple.com)
- [Google Play Store](https://play.google.com)
- [Steam](https://store.steampowered.com)
- [Epic Games Store](https://store.epicgames.com)

### صور الغلاف:
- [IGDB](https://www.igdb.com)
- [MobyGames](https://www.mobygames.com)
- [Steam Store Pages](https://store.steampowered.com)
- المواقع الرسمية للألعاب

## قائمة الألعاب المشمولة
المشروع يشمل 72 لعبة:
- ألعاب Battle Royale (Fortnite, PUBG Mobile, Free Fire)
- ألعاب الرياضة (DLS 25, eFootball, FC MOBILE)
- ألعاب الاستراتيجية (Clash of Clans, Lords Mobile)
- ألعاب الكازينو والألغاز (Coin Master, Candy Crush)
- تطبيقات البث المباشر (Bigo Live, Chamet)
- وأكثر...

## ملاحظات مهمة
- تأكد من احترام حقوق الطبع والنشر عند تحميل الصور
- استخدم الصور الرسمية فقط
- احفظ الصور بأسماء موحدة: `icon.png` و `cover.jpg`
- تأكد من جودة الصور قبل الحفظ

## استكشاف الأخطاء
- إذا لم يعمل سكريپت PowerShell، تأكد من تفعيل تشغيل السكريپتات:
  ```powershell
  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
  ```
- إذا لم يعمل سكريپت Python، تأكد من تثبيت Python 3.x
- للمساعدة في العثور على الصور، استخدم البحث بالكلمات المفتاحية المناسبة
