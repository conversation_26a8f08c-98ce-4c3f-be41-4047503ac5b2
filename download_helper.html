<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مساعد تحميل صور الألعاب</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .game-card {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            background: #fafafa;
        }
        .game-name {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .search-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .search-link {
            padding: 8px 15px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        .search-link:hover {
            background: #2980b9;
        }
        .icon-link {
            background: #e74c3c;
        }
        .icon-link:hover {
            background: #c0392b;
        }
        .cover-link {
            background: #27ae60;
        }
        .cover-link:hover {
            background: #229954;
        }
        .folder-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
            background: #ecf0f1;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .progress {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            color: #2c3e50;
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 مساعد تحميل صور الألعاب</h1>
        
        <div class="instructions">
            <h3>تعليمات الاستخدام:</h3>
            <ol>
                <li>اضغط على رابط البحث عن الأيقونة أو الغلاف</li>
                <li>احفظ الصورة في المجلد المحدد</li>
                <li>تأكد من تسمية الملفات: <code>icon.png</code> للأيقونة و <code>cover.jpg</code> للغلاف</li>
                <li>استخدم صور عالية الجودة (512x512+ للأيقونات، 1920x1080+ للأغلفة)</li>
            </ol>
        </div>

        <div class="progress">
            <span id="completed">0</span> / <span id="total">72</span> ألعاب مكتملة
        </div>

        <div id="games-container">
            <!-- سيتم ملء الألعاب هنا بواسطة JavaScript -->
        </div>
    </div>

    <script>
        const games = [
            "Fortnite", "PUBG Mobile", "Free Fire", "DLS 25", "eFootball", "FC MOBILE",
            "Roblox", "Monopoly GO", "Stumble Guys", "Brawl Stars", "Royal Match",
            "Lords Mobile", "Township", "Bingo Blitz", "Homescapes", "Blood Strike",
            "BASEBALL 9", "Match Master", "Dragon City", "Candy Crush Saga", "Mech Arena",
            "Cooking Fever", "Board Kings", "ZEPETO", "DRAGON BALL LEGENDS", "ONE PIECE",
            "Subway Surfers", "Clash of Clans", "8 Ball Pool", "Pokemon Go", "Car Parking",
            "Car Parking Multiplayer 2", "Chikii", "TopTop", "WePlay", "Jawaker",
            "Hay Day", "FROZEN CASH", "Yalla Ludo", "Ludo Star", "Ludo Club",
            "Blockman Go", "CarX Street", "Capybara Go", "PK XD", "Zooba",
            "Family Island", "Coin Master", "COD Mobile", "Blooket", "Hero Wars",
            "Hide Online", "HeadBall 2", "World of Tanks Blitz", "Uno", "BitLife",
            "Avakin Life", "Tango", "HalaMe", "Sugo", "Bigo Live", "Poppo Live",
            "Tigo", "Cafe", "Starchat", "Soulchill", "Chamet", "Azar", "Top Follow",
            "Timo", "Golf Battle", "Last War Survival", "SimCity BuildIt",
            "NBA Infinite", "FarmVille 2"
        ];

        function createSearchUrl(query) {
            return `https://www.google.com/search?q=${encodeURIComponent(query)}&tbm=isch`;
        }

        function createGameCard(gameName, index) {
            const iconQuery = `${gameName} game icon logo`;
            const coverQuery = `${gameName} game cover wallpaper`;
            
            return `
                <div class="game-card" id="game-${index}">
                    <div class="game-name">${gameName}</div>
                    <div class="search-links">
                        <a href="${createSearchUrl(iconQuery)}" target="_blank" class="search-link icon-link">
                            🔍 بحث عن الأيقونة
                        </a>
                        <a href="${createSearchUrl(coverQuery)}" target="_blank" class="search-link cover-link">
                            🔍 بحث عن الغلاف
                        </a>
                        <button onclick="markCompleted(${index})" class="search-link" style="background: #f39c12; border: none; cursor: pointer;">
                            ✅ تم الانتهاء
                        </button>
                    </div>
                    <div class="folder-info">
                        📁 المجلد: ${gameName}/img/ | الأيقونة: icon.png | الغلاف: cover.jpg
                    </div>
                </div>
            `;
        }

        function markCompleted(index) {
            const gameCard = document.getElementById(`game-${index}`);
            gameCard.style.opacity = '0.5';
            gameCard.style.background = '#d5f4e6';
            
            // تحديث العداد
            const completed = document.querySelectorAll('.game-card[style*="opacity: 0.5"]').length;
            document.getElementById('completed').textContent = completed;
            
            // حفظ التقدم في localStorage
            localStorage.setItem(`game-${index}-completed`, 'true');
        }

        function loadProgress() {
            games.forEach((game, index) => {
                if (localStorage.getItem(`game-${index}-completed`) === 'true') {
                    markCompleted(index);
                }
            });
        }

        // إنشاء بطاقات الألعاب
        const container = document.getElementById('games-container');
        games.forEach((game, index) => {
            container.innerHTML += createGameCard(game, index);
        });

        // تحديث العدد الإجمالي
        document.getElementById('total').textContent = games.length;

        // تحميل التقدم المحفوظ
        loadProgress();
    </script>
</body>
</html>
