<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مساعد تحميل صور الألعاب</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .game-card {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            background: #fafafa;
        }
        .game-name {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .search-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .search-link {
            padding: 8px 15px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        .search-link:hover {
            background: #2980b9;
        }
        .icon-link {
            background: #e74c3c;
        }
        .icon-link:hover {
            background: #c0392b;
        }
        .cover-link {
            background: #27ae60;
        }
        .cover-link:hover {
            background: #229954;
        }
        .folder-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
            background: #ecf0f1;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .progress {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            color: #2c3e50;
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 مساعد تحميل صور الألعاب</h1>
        
        <div class="instructions">
            <h3>تعليمات الاستخدام:</h3>
            <ol>
                <li>اضغط على رابط البحث للنوع المطلوب (أيقونة، عملة، خلفية)</li>
                <li>احفظ الصورة في المجلد المحدد</li>
                <li>تأكد من تسمية الملفات: <code>gameicon.png</code> و <code>currency.png</code> و <code>wallpaper.jpg</code></li>
                <li>استخدم الأحجام المحددة: 512x512 للأيقونات، 256x256 للعملات، 1920x1080 للخلفيات</li>
                <li>البحث محسن بأحجام محددة لنتائج أفضل</li>
            </ol>
        </div>

        <div class="progress">
            <span id="completed">0</span> / <span id="total">72</span> ألعاب مكتملة
        </div>

        <div id="games-container">
            <!-- سيتم ملء الألعاب هنا بواسطة JavaScript -->
        </div>
    </div>

    <script>
        const gamesData = {
            "Fortnite": {"currency": "V-Bucks"},
            "PUBG Mobile": {"currency": "UC"},
            "Free Fire": {"currency": "Diamonds"},
            "DLS 25": {"currency": "Coins"},
            "eFootball": {"currency": "eFootball Coins"},
            "FC MOBILE": {"currency": "FC Points"},
            "Roblox": {"currency": "Robux"},
            "Monopoly GO": {"currency": "Dice Rolls"},
            "Stumble Guys": {"currency": "Gems"},
            "Brawl Stars": {"currency": "Gems"},
            "Royal Match": {"currency": "Coins"},
            "Lords Mobile": {"currency": "Gems"},
            "Township": {"currency": "Coins"},
            "Bingo Blitz": {"currency": "Credits"},
            "Homescapes": {"currency": "Coins"},
            "Blood Strike": {"currency": "Diamonds"},
            "BASEBALL 9": {"currency": "Coins"},
            "Match Master": {"currency": "Coins"},
            "Dragon City": {"currency": "Gems"},
            "Candy Crush Saga": {"currency": "Gold Bars"},
            "Mech Arena": {"currency": "A-Coins"},
            "Cooking Fever": {"currency": "Gems"},
            "Board Kings": {"currency": "Rolls"},
            "ZEPETO": {"currency": "Coins"},
            "DRAGON BALL LEGENDS": {"currency": "Chrono Crystals"},
            "ONE PIECE": {"currency": "Rainbow Diamonds"},
            "Subway Surfers": {"currency": "Coins"},
            "Clash of Clans": {"currency": "Gems"},
            "8 Ball Pool": {"currency": "Coins"},
            "Pokemon Go": {"currency": "PokéCoins"},
            "Car Parking": {"currency": "Money"},
            "Car Parking Multiplayer 2": {"currency": "Money"},
            "Chikii": {"currency": "Coins"},
            "TopTop": {"currency": "Coins"},
            "WePlay": {"currency": "Coins"},
            "Jawaker": {"currency": "Chips"},
            "Hay Day": {"currency": "Diamonds"},
            "FROZEN CASH": {"currency": "Cash"},
            "Yalla Ludo": {"currency": "Diamonds"},
            "Ludo Star": {"currency": "Coins"},
            "Ludo Club": {"currency": "Coins"},
            "Blockman Go": {"currency": "Cubes"},
            "CarX Street": {"currency": "Credits"},
            "Capybara Go": {"currency": "Coins"},
            "PK XD": {"currency": "Gems"},
            "Zooba": {"currency": "Gems"},
            "Family Island": {"currency": "Rubies"},
            "Coin Master": {"currency": "Spins"},
            "COD Mobile": {"currency": "CP"},
            "Blooket": {"currency": "Tokens"},
            "Hero Wars": {"currency": "Emeralds"},
            "Hide Online": {"currency": "Coins"},
            "HeadBall 2": {"currency": "Diamonds"},
            "World of Tanks Blitz": {"currency": "Gold"},
            "Uno": {"currency": "Coins"},
            "BitLife": {"currency": "Bitizenship"},
            "Avakin Life": {"currency": "Avacoins"},
            "Tango": {"currency": "Coins"},
            "HalaMe": {"currency": "Coins"},
            "Sugo": {"currency": "Coins"},
            "Bigo Live": {"currency": "Diamonds"},
            "Poppo Live": {"currency": "Coins"},
            "Tigo": {"currency": "Coins"},
            "Cafe": {"currency": "Coins"},
            "Starchat": {"currency": "Coins"},
            "Soulchill": {"currency": "Diamonds"},
            "Chamet": {"currency": "Diamonds"},
            "Azar": {"currency": "Gems"},
            "Top Follow": {"currency": "Coins"},
            "Timo": {"currency": "Coins"},
            "Golf Battle": {"currency": "Gems"},
            "Last War Survival": {"currency": "Diamonds"},
            "SimCity BuildIt": {"currency": "SimCash"},
            "NBA Infinite": {"currency": "Coins"},
            "FarmVille 2": {"currency": "Farm Bucks"}
        };

        const games = Object.keys(gamesData);

        function createSearchUrl(query, size = "") {
            let url = `https://www.google.com/search?q=${encodeURIComponent(query)}&tbm=isch`;
            if (size) {
                const [width, height] = size.split('x');
                url += `&tbs=isz:ex,iszw:${width},iszh:${height}`;
            }
            return url;
        }

        function createGameCard(gameName, index) {
            const currency = gamesData[gameName].currency;
            const iconQuery = `${gameName} game icon logo 512x512`;
            const currencyQuery = `${gameName} ${currency} icon currency`;
            const wallpaperQuery = `${gameName} game wallpaper 1920x1080`;

            return `
                <div class="game-card" id="game-${index}">
                    <div class="game-name">${gameName} - ${currency}</div>
                    <div class="search-links">
                        <a href="${createSearchUrl(iconQuery, '512x512')}" target="_blank" class="search-link icon-link">
                            🎮 أيقونة اللعبة (512x512)
                        </a>
                        <a href="${createSearchUrl(currencyQuery, '256x256')}" target="_blank" class="search-link" style="background: #9b59b6;">
                            💰 عملة اللعبة (256x256)
                        </a>
                        <a href="${createSearchUrl(wallpaperQuery, '1920x1080')}" target="_blank" class="search-link cover-link">
                            �️ خلفية اللعبة (1920x1080)
                        </a>
                        <button onclick="markCompleted(${index})" class="search-link" style="background: #f39c12; border: none; cursor: pointer;">
                            ✅ تم الانتهاء
                        </button>
                    </div>
                    <div class="folder-info">
                        📁 المجلد: ${gameName}/img/ | الأيقونة: gameicon.png | العملة: currency.png | الخلفية: wallpaper.jpg
                    </div>
                </div>
            `;
        }

        function markCompleted(index) {
            const gameCard = document.getElementById(`game-${index}`);
            gameCard.style.opacity = '0.5';
            gameCard.style.background = '#d5f4e6';
            
            // تحديث العداد
            const completed = document.querySelectorAll('.game-card[style*="opacity: 0.5"]').length;
            document.getElementById('completed').textContent = completed;
            
            // حفظ التقدم في localStorage
            localStorage.setItem(`game-${index}-completed`, 'true');
        }

        function loadProgress() {
            games.forEach((game, index) => {
                if (localStorage.getItem(`game-${index}-completed`) === 'true') {
                    markCompleted(index);
                }
            });
        }

        // إنشاء بطاقات الألعاب
        const container = document.getElementById('games-container');
        games.forEach((game, index) => {
            container.innerHTML += createGameCard(game, index);
        });

        // تحديث العدد الإجمالي
        document.getElementById('total').textContent = games.length;

        // تحميل التقدم المحفوظ
        loadProgress();
    </script>
</body>
</html>
