#!/usr/bin/env python3
"""
Create Local Placeholder Images
Creates simple colored placeholder images locally without internet
"""

import os
from PIL import Image, ImageDraw, ImageFont
import json

# Complete games data
GAMES_DATA = {
    "Fortnite": {"currency": "V-Bucks", "color": "#FF6B35"},
    "PUBG Mobile": {"currency": "UC", "color": "#FF8C00"},
    "Free Fire": {"currency": "Diamonds", "color": "#FF4500"},
    "DLS 25": {"currency": "Coins", "color": "#32CD32"},
    "eFootball": {"currency": "eFootball Coins", "color": "#228B22"},
    "FC MOBILE": {"currency": "FC Points", "color": "#006400"},
    "Roblox": {"currency": "Robux", "color": "#FF1493"},
    "Monopoly GO": {"currency": "Dice Rolls", "color": "#8B008B"},
    "Stumble Guys": {"currency": "Gems", "color": "#9932CC"},
    "Brawl Stars": {"currency": "Gems", "color": "#4169E1"},
    "Royal Match": {"currency": "Coins", "color": "#1E90FF"},
    "Lords Mobile": {"currency": "Gems", "color": "#00BFFF"},
    "Township": {"currency": "Coins", "color": "#87CEEB"},
    "Bingo Blitz": {"currency": "Credits", "color": "#DC143C"},
    "Homescapes": {"currency": "Coins", "color": "#B22222"},
    "Blood Strike": {"currency": "Diamonds", "color": "#8B0000"},
    "BASEBALL 9": {"currency": "Coins", "color": "#FFD700"},
    "Match Master": {"currency": "Coins", "color": "#FFA500"},
    "Dragon City": {"currency": "Gems", "color": "#FF8C00"},
    "Candy Crush Saga": {"currency": "Gold Bars", "color": "#FF69B4"},
    "Mech Arena": {"currency": "A-Coins", "color": "#708090"},
    "Cooking Fever": {"currency": "Gems", "color": "#FF6347"},
    "Board Kings": {"currency": "Rolls", "color": "#40E0D0"},
    "ZEPETO": {"currency": "Coins", "color": "#DA70D6"},
    "DRAGON BALL LEGENDS": {"currency": "Chrono Crystals", "color": "#FF4500"},
    "ONE PIECE": {"currency": "Rainbow Diamonds", "color": "#FF1493"},
    "Subway Surfers": {"currency": "Coins", "color": "#32CD32"},
    "Clash of Clans": {"currency": "Gems", "color": "#4169E1"},
    "8 Ball Pool": {"currency": "Coins", "color": "#000080"},
    "Pokemon Go": {"currency": "PokéCoins", "color": "#FFD700"},
    "Car Parking": {"currency": "Money", "color": "#808080"},
    "Car Parking Multiplayer 2": {"currency": "Money", "color": "#696969"},
    "Chikii": {"currency": "Coins", "color": "#FF6B35"},
    "TopTop": {"currency": "Coins", "color": "#FF8C00"},
    "WePlay": {"currency": "Coins", "color": "#FF4500"},
    "Jawaker": {"currency": "Chips", "color": "#32CD32"},
    "Hay Day": {"currency": "Diamonds", "color": "#228B22"},
    "FROZEN CASH": {"currency": "Cash", "color": "#87CEEB"},
    "Yalla Ludo": {"currency": "Diamonds", "color": "#FF1493"},
    "Ludo Star": {"currency": "Coins", "color": "#8B008B"},
    "Ludo Club": {"currency": "Coins", "color": "#9932CC"},
    "Blockman Go": {"currency": "Cubes", "color": "#4169E1"},
    "CarX Street": {"currency": "Credits", "color": "#1E90FF"},
    "Capybara Go": {"currency": "Coins", "color": "#00BFFF"},
    "PK XD": {"currency": "Gems", "color": "#DC143C"},
    "Zooba": {"currency": "Gems", "color": "#B22222"},
    "Family Island": {"currency": "Rubies", "color": "#8B0000"},
    "Coin Master": {"currency": "Spins", "color": "#FFD700"},
    "COD Mobile": {"currency": "CP", "color": "#FFA500"},
    "Blooket": {"currency": "Tokens", "color": "#FF8C00"},
    "Hero Wars": {"currency": "Emeralds", "color": "#FF69B4"},
    "Hide Online": {"currency": "Coins", "color": "#708090"},
    "HeadBall 2": {"currency": "Diamonds", "color": "#FF6347"},
    "World of Tanks Blitz": {"currency": "Gold", "color": "#40E0D0"},
    "Uno": {"currency": "Coins", "color": "#DA70D6"},
    "BitLife": {"currency": "Bitizenship", "color": "#FF4500"},
    "Avakin Life": {"currency": "Avacoins", "color": "#FF1493"},
    "Tango": {"currency": "Coins", "color": "#32CD32"},
    "HalaMe": {"currency": "Coins", "color": "#4169E1"},
    "Sugo": {"currency": "Coins", "color": "#000080"},
    "Bigo Live": {"currency": "Diamonds", "color": "#FFD700"},
    "Poppo Live": {"currency": "Coins", "color": "#808080"},
    "Tigo": {"currency": "Coins", "color": "#696969"},
    "Cafe": {"currency": "Coins", "color": "#FF6B35"},
    "Starchat": {"currency": "Coins", "color": "#FF8C00"},
    "Soulchill": {"currency": "Diamonds", "color": "#FF4500"},
    "Chamet": {"currency": "Diamonds", "color": "#32CD32"},
    "Azar": {"currency": "Gems", "color": "#228B22"},
    "Top Follow": {"currency": "Coins", "color": "#87CEEB"},
    "Timo": {"currency": "Coins", "color": "#FF1493"},
    "Golf Battle": {"currency": "Gems", "color": "#8B008B"},
    "Last War Survival": {"currency": "Diamonds", "color": "#9932CC"},
    "SimCity BuildIt": {"currency": "SimCash", "color": "#4169E1"},
    "NBA Infinite": {"currency": "Coins", "color": "#1E90FF"},
    "FarmVille 2": {"currency": "Farm Bucks", "color": "#00BFFF"}
}

def create_image_with_text(width, height, bg_color, text, text_color="white", font_size=None):
    """Create an image with text"""
    try:
        # Create image
        img = Image.new('RGB', (width, height), bg_color)
        draw = ImageDraw.Draw(img)
        
        # Calculate font size if not provided
        if font_size is None:
            font_size = min(width, height) // 8
        
        # Try to use a better font, fallback to default
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", font_size)
            except:
                font = ImageFont.load_default()
        
        # Get text size and center it
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        # Draw text
        draw.text((x, y), text, fill=text_color, font=font)
        
        return img
    except Exception as e:
        print(f"Error creating image: {e}")
        # Create simple colored rectangle as fallback
        return Image.new('RGB', (width, height), bg_color)

def create_game_images(game_name, game_info):
    """Create all three images for a game"""
    game_folder = os.path.join(game_name, "img")
    
    if not os.path.exists(game_folder):
        print(f"❌ Folder not found: {game_folder}")
        return False
    
    currency = game_info['currency']
    color = game_info['color']
    
    success_count = 0
    
    # Create gameicon (512x512)
    icon_path = os.path.join(game_folder, "gameicon.png")
    if not os.path.exists(icon_path):
        try:
            icon_img = create_image_with_text(512, 512, color, game_name[:10], "white", 40)
            icon_img.save(icon_path, "PNG")
            print(f"  ✅ Created gameicon.png")
            success_count += 1
        except Exception as e:
            print(f"  ❌ Failed to create gameicon: {e}")
    else:
        print(f"  ⏭️  gameicon.png already exists")
        success_count += 1
    
    # Create currency (256x256)
    currency_path = os.path.join(game_folder, "currency.png")
    if not os.path.exists(currency_path):
        try:
            # Use a different shade for currency
            currency_color = "#FFD700"  # Gold color for currency
            currency_img = create_image_with_text(256, 256, currency_color, currency[:8], "black", 20)
            currency_img.save(currency_path, "PNG")
            print(f"  ✅ Created currency.png")
            success_count += 1
        except Exception as e:
            print(f"  ❌ Failed to create currency: {e}")
    else:
        print(f"  ⏭️  currency.png already exists")
        success_count += 1
    
    # Create wallpaper (1920x1080)
    wallpaper_path = os.path.join(game_folder, "wallpaper.jpg")
    if not os.path.exists(wallpaper_path):
        try:
            # Create gradient-like effect by using darker shade
            wallpaper_color = color.replace("#", "#2")  # Make it darker
            if len(wallpaper_color) < 7:
                wallpaper_color = "#2196F3"  # Fallback blue
            
            wallpaper_img = create_image_with_text(1920, 1080, wallpaper_color, game_name, "white", 80)
            wallpaper_img.save(wallpaper_path, "JPEG", quality=85)
            print(f"  ✅ Created wallpaper.jpg")
            success_count += 1
        except Exception as e:
            print(f"  ❌ Failed to create wallpaper: {e}")
    else:
        print(f"  ⏭️  wallpaper.jpg already exists")
        success_count += 1
    
    return success_count == 3

def main():
    print("🎨 Local Image Creator")
    print("=" * 30)
    
    # Check if PIL is available
    try:
        from PIL import Image
        print("✅ PIL (Pillow) is available")
    except ImportError:
        print("❌ PIL (Pillow) not found. Installing...")
        import subprocess
        subprocess.check_call(["pip", "install", "Pillow"])
        print("✅ PIL (Pillow) installed")
    
    total_games = len(GAMES_DATA)
    completed_games = 0
    
    print(f"\n🚀 Creating images for {total_games} games...")
    
    for game_name, game_info in GAMES_DATA.items():
        print(f"\n🎮 Processing: {game_name}")
        
        if create_game_images(game_name, game_info):
            completed_games += 1
            print(f"  ✅ All images created for {game_name}")
        else:
            print(f"  ⚠️  Some images missing for {game_name}")
    
    print(f"\n📊 Summary:")
    print(f"✅ Completed games: {completed_games}/{total_games}")
    print(f"📈 Success rate: {(completed_games/total_games*100):.1f}%")
    
    print(f"\n🎉 Process completed!")
    print(f"📁 All games now have local placeholder images")
    print(f"🌐 Use download_helper.html to replace with real images")

if __name__ == "__main__":
    main()
