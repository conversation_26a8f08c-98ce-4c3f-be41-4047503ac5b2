#!/usr/bin/env python3
"""
Advanced Image Downloader using multiple sources
Downloads gameicon, currency, and wallpaper for each game
"""

import os
import requests
import json
import time
from urllib.parse import quote
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# Game data with currencies
GAMES_DATA = {
    "Fortnite": {"currency": "V-Bucks", "app_id": "1261357853"},
    "PUBG Mobile": {"currency": "UC", "app_id": "1330123889"},
    "Free Fire": {"currency": "Diamonds", "app_id": "1300146617"},
    "Roblox": {"currency": "Robux", "app_id": "431946152"},
    "Clash of Clans": {"currency": "Gems", "app_id": "529479190"},
    "Candy Crush Saga": {"currency": "Gold Bars", "app_id": "553834731"},
    "Pokemon Go": {"currency": "PokéCoins", "app_id": "1094591345"},
    "Subway Surfers": {"currency": "Coins", "app_id": "512939461"},
    "COD Mobile": {"currency": "CP", "app_id": "1287282214"},
    "Brawl Stars": {"currency": "Gems", "app_id": "1229016807"}
}

class GameImageDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.download_stats = {"success": 0, "failed": 0, "total": 0}

    def download_image(self, url, filepath, timeout=30):
        """Download image with error handling"""
        try:
            response = self.session.get(url, timeout=timeout, stream=True)
            response.raise_for_status()
            
            # Verify content type
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                return False, f"Not an image: {content_type}"
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # Verify file size
            if os.path.getsize(filepath) < 1024:  # Less than 1KB
                os.remove(filepath)
                return False, "File too small"
            
            return True, "Success"
        except Exception as e:
            return False, str(e)

    def get_app_store_icon(self, app_id):
        """Get icon from App Store API"""
        try:
            url = f"https://itunes.apple.com/lookup?id={app_id}"
            response = self.session.get(url, timeout=10)
            data = response.json()
            
            if data['resultCount'] > 0:
                icon_url = data['results'][0].get('artworkUrl512', '')
                if icon_url:
                    return icon_url.replace('512x512bb.jpg', '1024x1024bb.png')
            return None
        except:
            return None

    def search_unsplash(self, query, width=1920, height=1080):
        """Search Unsplash for wallpapers (requires API key)"""
        # This would require Unsplash API key
        # For demo purposes, return None
        return None

    def get_placeholder_images(self, game_name, currency):
        """Generate placeholder image URLs"""
        base_url = "https://via.placeholder.com"
        return {
            'gameicon': f"{base_url}/512x512/4CAF50/FFFFFF?text={quote(game_name[:10])}",
            'currency': f"{base_url}/256x256/FF9800/FFFFFF?text={quote(currency[:8])}",
            'wallpaper': f"{base_url}/1920x1080/2196F3/FFFFFF?text={quote(game_name[:15])}"
        }

    def download_game_images(self, game_name, game_info):
        """Download all images for a single game"""
        print(f"\n🎮 Processing: {game_name}")
        
        game_folder = os.path.join(game_name, "img")
        if not os.path.exists(game_folder):
            print(f"❌ Folder not found: {game_folder}")
            return False
        
        currency = game_info['currency']
        app_id = game_info.get('app_id')
        
        results = {}
        
        # Try to get App Store icon
        if app_id:
            icon_url = self.get_app_store_icon(app_id)
            if icon_url:
                icon_path = os.path.join(game_folder, "gameicon.png")
                success, msg = self.download_image(icon_url, icon_path)
                results['gameicon'] = success
                if success:
                    print(f"✅ Downloaded gameicon from App Store")
                else:
                    print(f"❌ Failed to download gameicon: {msg}")
            else:
                results['gameicon'] = False
        else:
            results['gameicon'] = False
        
        # Get placeholder images for missing ones
        placeholders = self.get_placeholder_images(game_name, currency)
        
        # Download currency icon (placeholder)
        if not results.get('gameicon'):
            currency_path = os.path.join(game_folder, "gameicon.png")
            success, msg = self.download_image(placeholders['gameicon'], currency_path)
            results['gameicon'] = success
            if success:
                print(f"✅ Downloaded gameicon placeholder")
        
        # Download currency placeholder
        currency_path = os.path.join(game_folder, "currency.png")
        success, msg = self.download_image(placeholders['currency'], currency_path)
        results['currency'] = success
        if success:
            print(f"✅ Downloaded currency placeholder")
        
        # Download wallpaper placeholder
        wallpaper_path = os.path.join(game_folder, "wallpaper.jpg")
        success, msg = self.download_image(placeholders['wallpaper'], wallpaper_path)
        results['wallpaper'] = success
        if success:
            print(f"✅ Downloaded wallpaper placeholder")
        
        # Update stats
        for result in results.values():
            self.download_stats['total'] += 1
            if result:
                self.download_stats['success'] += 1
            else:
                self.download_stats['failed'] += 1
        
        return all(results.values())

    def create_batch_download_script(self):
        """Create a batch script for manual downloading"""
        script_content = """@echo off
echo Game Images Batch Downloader
echo ===========================
echo.
echo This script will help you download images for each game.
echo Press any key to continue or Ctrl+C to exit.
pause
echo.

"""
        
        for game_name, game_info in GAMES_DATA.items():
            currency = game_info['currency']
            script_content += f"""
echo Processing: {game_name}
echo Currency: {currency}
echo.
echo Required files:
echo 1. {game_name}\\img\\gameicon.png (512x512)
echo 2. {game_name}\\img\\currency.png (256x256)  
echo 3. {game_name}\\img\\wallpaper.jpg (1920x1080)
echo.
echo Search suggestions:
echo - "{game_name} game icon 512x512"
echo - "{game_name} {currency} icon"
echo - "{game_name} game wallpaper 1920x1080"
echo.
pause
"""
        
        with open("batch_download_helper.bat", "w", encoding="utf-8") as f:
            f.write(script_content)
        
        print("✅ Created batch_download_helper.bat")

    def run_download_process(self, max_workers=3):
        """Run the download process with threading"""
        print("🚀 Starting Advanced Game Image Downloader")
        print("=" * 50)
        
        # Create batch script
        self.create_batch_download_script()
        
        # Ask user for download method
        print("\nDownload Options:")
        print("1. Download placeholder images (FAST)")
        print("2. Download real images where possible (SLOW)")
        print("3. Create download scripts only")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "3":
            print("✅ Scripts created successfully!")
            return
        
        if choice not in ["1", "2"]:
            print("Invalid choice. Using option 1 (placeholders)")
            choice = "1"
        
        print(f"\n🎯 Starting download process...")
        
        if choice == "1":
            # Download placeholders for all games
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {
                    executor.submit(self.download_game_images, game_name, game_info): game_name
                    for game_name, game_info in GAMES_DATA.items()
                }
                
                for future in as_completed(futures):
                    game_name = futures[future]
                    try:
                        result = future.result()
                        if result:
                            print(f"✅ Completed: {game_name}")
                        else:
                            print(f"⚠️  Partial completion: {game_name}")
                    except Exception as e:
                        print(f"❌ Error processing {game_name}: {e}")
        
        # Print statistics
        print(f"\n📊 Download Statistics:")
        print(f"✅ Successful: {self.download_stats['success']}")
        print(f"❌ Failed: {self.download_stats['failed']}")
        print(f"📝 Total: {self.download_stats['total']}")
        
        if self.download_stats['total'] > 0:
            success_rate = (self.download_stats['success'] / self.download_stats['total']) * 100
            print(f"📈 Success Rate: {success_rate:.1f}%")

def main():
    downloader = GameImageDownloader()
    downloader.run_download_process()
    
    print("\n🎉 Process completed!")
    print("📁 Check your game folders for downloaded images")
    print("🌐 Use download_helper.html for manual downloading")
    print("📋 Use batch_download_helper.bat for guided manual process")

if __name__ == "__main__":
    main()
