#!/usr/bin/env python3
"""
Script to download game icons and cover images automatically
"""

import os
import requests
import time
from urllib.parse import quote
import json

# List of games
games = [
    "Fortnite", "PUBG Mobile", "Free Fire", "DLS 25", "eFootball", "FC MOBILE",
    "Roblox", "Monopoly GO", "Stumble Guys", "Brawl Stars", "Royal Match",
    "Lords Mobile", "Township", "Bingo Blitz", "Homescapes", "Blood Strike",
    "BASEBALL 9", "Match Master", "Dragon City", "Candy Crush Saga", "Mech Arena",
    "Cooking Fever", "Board Kings", "ZEPETO", "DRAGON BALL LEGENDS", "ONE PIECE",
    "Subway Surfers", "Clash of Clans", "8 Ball Pool", "Pokemon Go", "Car Parking",
    "Car Parking Multiplayer 2", "Chikii", "TopTop", "WePlay", "Jawaker",
    "Hay Day", "FROZ<PERSON> CASH", "Yalla Ludo", "Ludo Star", "Ludo Club",
    "Blockman Go", "CarX Street", "Capybara Go", "PK XD", "Zooba",
    "Family Island", "Coin Master", "COD Mobile", "Blooket", "Hero Wars",
    "Hide Online", "HeadBall 2", "World of Tanks Blitz", "Uno", "BitLife",
    "Avakin Life", "Tango", "HalaMe", "Sugo", "Bigo Live", "Poppo Live",
    "Tigo", "Cafe", "Starchat", "Soulchill", "Chamet", "Azar", "Top Follow",
    "Timo", "Golf Battle", "Last War Survival", "SimCity BuildIt",
    "NBA Infinite", "FarmVille 2"
]

def download_image(url, filepath):
    """Download an image from URL to filepath"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        with open(filepath, 'wb') as f:
            f.write(response.content)
        return True
    except Exception as e:
        print(f"Error downloading {url}: {e}")
        return False

def search_game_images(game_name):
    """
    This function would need to be implemented with a proper image search API
    For now, it returns placeholder URLs that you would need to replace
    """
    # You would need to implement this with:
    # - Google Custom Search API
    # - Bing Image Search API
    # - Or manually curated URLs
    
    return {
        'icon': f"https://example.com/placeholder_icon_{quote(game_name)}.png",
        'cover': f"https://example.com/placeholder_cover_{quote(game_name)}.jpg"
    }

def create_download_script():
    """Create a batch script for manual download"""
    script_content = """@echo off
echo Game Image Download Helper
echo ========================
echo.
echo This script will help you download images for each game.
echo For each game, you'll need to:
echo 1. Search for the game icon (512x512 or 1024x1024)
echo 2. Search for the game cover image (1920x1080 or similar)
echo 3. Save them in the correct folders
echo.
pause

"""
    
    for game in games:
        script_content += f"""
echo Processing: {game}
echo Icon should go to: "{game}\\img\\icon.png"
echo Cover should go to: "{game}\\img\\cover.jpg"
echo.
echo Search URLs:
echo Google Images: https://images.google.com/search?q={quote(game + " game icon")}
echo Google Images: https://images.google.com/search?q={quote(game + " game cover")}
echo.
pause
"""
    
    with open("manual_download_helper.bat", "w", encoding="utf-8") as f:
        f.write(script_content)

def main():
    print("Game Image Downloader")
    print("====================")
    
    # Create manual download helper
    create_download_script()
    print("Created manual_download_helper.bat")
    
    # Create a JSON file with game information for easier processing
    game_info = []
    for game in games:
        game_info.append({
            "name": game,
            "folder": game,
            "icon_path": f"{game}/img/icon.png",
            "cover_path": f"{game}/img/cover.jpg",
            "search_terms": {
                "icon": f"{game} game icon",
                "cover": f"{game} game cover"
            }
        })
    
    with open("games_info.json", "w", encoding="utf-8") as f:
        json.dump(game_info, f, indent=2, ensure_ascii=False)
    
    print("Created games_info.json with game information")
    print(f"Total games to process: {len(games)}")
    
    print("\nNext steps:")
    print("1. Run create_game_folders.ps1 to create folder structure")
    print("2. Use manual_download_helper.bat for guided manual download")
    print("3. Or implement automatic download with proper image search API")

if __name__ == "__main__":
    main()
