@echo off
echo Creating game folders...

mkdir "Fortnite\img" 2>nul
mkdir "PUBG Mobile\img" 2>nul
mkdir "Free Fire\img" 2>nul
mkdir "DLS 25\img" 2>nul
mkdir "eFootball\img" 2>nul
mkdir "FC MOBILE\img" 2>nul
mkdir "Roblox\img" 2>nul
mkdir "Monopoly GO\img" 2>nul
mkdir "Stumble Guys\img" 2>nul
mkdir "Brawl Stars\img" 2>nul
mkdir "Royal Match\img" 2>nul
mkdir "Lords Mobile\img" 2>nul
mkdir "Township\img" 2>nul
mkdir "Bingo Blitz\img" 2>nul
mkdir "Homescapes\img" 2>nul
mkdir "Blood Strike\img" 2>nul
mkdir "BASEBALL 9\img" 2>nul
mkdir "Match Master\img" 2>nul
mkdir "Dragon City\img" 2>nul
mkdir "Candy Crush Saga\img" 2>nul
mkdir "Mech Arena\img" 2>nul
mkdir "Cooking Fever\img" 2>nul
mkdir "Board Kings\img" 2>nul
mkdir "ZEPETO\img" 2>nul
mkdir "DRAGON BALL LEGENDS\img" 2>nul
mkdir "ONE PIECE\img" 2>nul
mkdir "Subway Surfers\img" 2>nul
mkdir "Clash of Clans\img" 2>nul
mkdir "8 Ball Pool\img" 2>nul
mkdir "Pokemon Go\img" 2>nul
mkdir "Car Parking\img" 2>nul
mkdir "Car Parking Multiplayer 2\img" 2>nul
mkdir "Chikii\img" 2>nul
mkdir "TopTop\img" 2>nul
mkdir "WePlay\img" 2>nul
mkdir "Jawaker\img" 2>nul
mkdir "Hay Day\img" 2>nul
mkdir "FROZEN CASH\img" 2>nul
mkdir "Yalla Ludo\img" 2>nul
mkdir "Ludo Star\img" 2>nul
mkdir "Ludo Club\img" 2>nul
mkdir "Blockman Go\img" 2>nul
mkdir "CarX Street\img" 2>nul
mkdir "Capybara Go\img" 2>nul
mkdir "PK XD\img" 2>nul
mkdir "Zooba\img" 2>nul
mkdir "Family Island\img" 2>nul
mkdir "Coin Master\img" 2>nul
mkdir "COD Mobile\img" 2>nul
mkdir "Blooket\img" 2>nul
mkdir "Hero Wars\img" 2>nul
mkdir "Hide Online\img" 2>nul
mkdir "HeadBall 2\img" 2>nul
mkdir "World of Tanks Blitz\img" 2>nul
mkdir "Uno\img" 2>nul
mkdir "BitLife\img" 2>nul
mkdir "Avakin Life\img" 2>nul
mkdir "Tango\img" 2>nul
mkdir "HalaMe\img" 2>nul
mkdir "Sugo\img" 2>nul
mkdir "Bigo Live\img" 2>nul
mkdir "Poppo Live\img" 2>nul
mkdir "Tigo\img" 2>nul
mkdir "Cafe\img" 2>nul
mkdir "Starchat\img" 2>nul
mkdir "Soulchill\img" 2>nul
mkdir "Chamet\img" 2>nul
mkdir "Azar\img" 2>nul
mkdir "Top Follow\img" 2>nul
mkdir "Timo\img" 2>nul
mkdir "Golf Battle\img" 2>nul
mkdir "Last War Survival\img" 2>nul
mkdir "SimCity BuildIt\img" 2>nul
mkdir "NBA Infinite\img" 2>nul
mkdir "FarmVille 2\img" 2>nul

echo.
echo All game folders created successfully!
echo Total: 72 games
echo.
echo Each folder contains an 'img' subfolder for icons and covers.
echo.
pause
