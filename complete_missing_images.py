#!/usr/bin/env python3
"""
Complete Missing Images - Downloads placeholder images for missing files
"""

import os
import requests
from urllib.parse import quote
import json

# Complete games data
GAMES_DATA = {
    "Fortnite": {"currency": "V-Bucks"},
    "PUBG Mobile": {"currency": "UC"},
    "Free Fire": {"currency": "Diamonds"},
    "DLS 25": {"currency": "Coins"},
    "eFootball": {"currency": "eFootball Coins"},
    "FC MOBILE": {"currency": "FC Points"},
    "Roblox": {"currency": "Robux"},
    "Monopoly GO": {"currency": "Dice Rolls"},
    "Stumble Guys": {"currency": "Gems"},
    "Brawl Stars": {"currency": "Gems"},
    "Royal Match": {"currency": "Coins"},
    "Lords Mobile": {"currency": "Gems"},
    "Township": {"currency": "Coins"},
    "Bingo Blitz": {"currency": "Credits"},
    "Homescapes": {"currency": "Coins"},
    "Blood Strike": {"currency": "Diamonds"},
    "BASEBALL 9": {"currency": "Coins"},
    "Match Master": {"currency": "Coins"},
    "Dragon City": {"currency": "Gems"},
    "Candy Crush Saga": {"currency": "Gold Bars"},
    "Mech Arena": {"currency": "A-Coins"},
    "Cooking Fever": {"currency": "Gems"},
    "Board Kings": {"currency": "Rolls"},
    "ZEPETO": {"currency": "Coins"},
    "DRAGON BALL LEGENDS": {"currency": "Chrono Crystals"},
    "ONE PIECE": {"currency": "Rainbow Diamonds"},
    "Subway Surfers": {"currency": "Coins"},
    "Clash of Clans": {"currency": "Gems"},
    "8 Ball Pool": {"currency": "Coins"},
    "Pokemon Go": {"currency": "PokéCoins"},
    "Car Parking": {"currency": "Money"},
    "Car Parking Multiplayer 2": {"currency": "Money"},
    "Chikii": {"currency": "Coins"},
    "TopTop": {"currency": "Coins"},
    "WePlay": {"currency": "Coins"},
    "Jawaker": {"currency": "Chips"},
    "Hay Day": {"currency": "Diamonds"},
    "FROZEN CASH": {"currency": "Cash"},
    "Yalla Ludo": {"currency": "Diamonds"},
    "Ludo Star": {"currency": "Coins"},
    "Ludo Club": {"currency": "Coins"},
    "Blockman Go": {"currency": "Cubes"},
    "CarX Street": {"currency": "Credits"},
    "Capybara Go": {"currency": "Coins"},
    "PK XD": {"currency": "Gems"},
    "Zooba": {"currency": "Gems"},
    "Family Island": {"currency": "Rubies"},
    "Coin Master": {"currency": "Spins"},
    "COD Mobile": {"currency": "CP"},
    "Blooket": {"currency": "Tokens"},
    "Hero Wars": {"currency": "Emeralds"},
    "Hide Online": {"currency": "Coins"},
    "HeadBall 2": {"currency": "Diamonds"},
    "World of Tanks Blitz": {"currency": "Gold"},
    "Uno": {"currency": "Coins"},
    "BitLife": {"currency": "Bitizenship"},
    "Avakin Life": {"currency": "Avacoins"},
    "Tango": {"currency": "Coins"},
    "HalaMe": {"currency": "Coins"},
    "Sugo": {"currency": "Coins"},
    "Bigo Live": {"currency": "Diamonds"},
    "Poppo Live": {"currency": "Coins"},
    "Tigo": {"currency": "Coins"},
    "Cafe": {"currency": "Coins"},
    "Starchat": {"currency": "Coins"},
    "Soulchill": {"currency": "Diamonds"},
    "Chamet": {"currency": "Diamonds"},
    "Azar": {"currency": "Gems"},
    "Top Follow": {"currency": "Coins"},
    "Timo": {"currency": "Coins"},
    "Golf Battle": {"currency": "Gems"},
    "Last War Survival": {"currency": "Diamonds"},
    "SimCity BuildIt": {"currency": "SimCash"},
    "NBA Infinite": {"currency": "Coins"},
    "FarmVille 2": {"currency": "Farm Bucks"}
}

def download_image(url, filepath):
    """Download image with error handling"""
    try:
        response = requests.get(url, timeout=30, stream=True)
        response.raise_for_status()
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        return True
    except Exception as e:
        print(f"Error downloading {url}: {e}")
        return False

def create_placeholder_urls(game_name, currency):
    """Create placeholder image URLs"""
    # Using different placeholder services for variety
    game_short = game_name[:12].replace(" ", "+")
    currency_short = currency[:10].replace(" ", "+")
    
    return {
        'gameicon': f"https://via.placeholder.com/512x512/4CAF50/FFFFFF?text={quote(game_short)}",
        'currency': f"https://via.placeholder.com/256x256/FF9800/FFFFFF?text={quote(currency_short)}",
        'wallpaper': f"https://via.placeholder.com/1920x1080/2196F3/FFFFFF?text={quote(game_name[:20])}"
    }

def check_missing_files(game_name):
    """Check which files are missing for a game"""
    game_folder = os.path.join(game_name, "img")
    if not os.path.exists(game_folder):
        return ['gameicon', 'currency', 'wallpaper']
    
    missing = []
    files_to_check = {
        'gameicon': 'gameicon.png',
        'currency': 'currency.png',
        'wallpaper': 'wallpaper.jpg'
    }
    
    for file_type, filename in files_to_check.items():
        filepath = os.path.join(game_folder, filename)
        if not os.path.exists(filepath):
            missing.append(file_type)
    
    return missing

def complete_missing_images():
    """Download placeholder images for all missing files"""
    print("🔍 Checking for missing images...")
    
    total_missing = 0
    total_downloaded = 0
    
    for game_name, game_info in GAMES_DATA.items():
        missing_files = check_missing_files(game_name)
        
        if missing_files:
            print(f"\n🎮 {game_name} - Missing: {', '.join(missing_files)}")
            currency = game_info['currency']
            placeholder_urls = create_placeholder_urls(game_name, currency)
            
            game_folder = os.path.join(game_name, "img")
            
            for file_type in missing_files:
                total_missing += 1
                
                if file_type == 'gameicon':
                    filepath = os.path.join(game_folder, "gameicon.png")
                elif file_type == 'currency':
                    filepath = os.path.join(game_folder, "currency.png")
                else:  # wallpaper
                    filepath = os.path.join(game_folder, "wallpaper.jpg")
                
                url = placeholder_urls[file_type]
                if download_image(url, filepath):
                    print(f"  ✅ Downloaded {file_type}")
                    total_downloaded += 1
                else:
                    print(f"  ❌ Failed to download {file_type}")
        else:
            print(f"✅ {game_name} - All files present")
    
    print(f"\n📊 Summary:")
    print(f"📝 Total missing files: {total_missing}")
    print(f"✅ Successfully downloaded: {total_downloaded}")
    print(f"❌ Failed downloads: {total_missing - total_downloaded}")
    
    if total_downloaded > 0:
        success_rate = (total_downloaded / total_missing) * 100
        print(f"📈 Success rate: {success_rate:.1f}%")

def create_status_report():
    """Create a detailed status report"""
    report = {
        "total_games": len(GAMES_DATA),
        "games_status": {},
        "summary": {
            "complete_games": 0,
            "partial_games": 0,
            "empty_games": 0,
            "total_files": 0,
            "existing_files": 0
        }
    }
    
    for game_name, game_info in GAMES_DATA.items():
        missing_files = check_missing_files(game_name)
        existing_files = 3 - len(missing_files)
        
        status = "complete" if len(missing_files) == 0 else "partial" if existing_files > 0 else "empty"
        
        report["games_status"][game_name] = {
            "currency": game_info["currency"],
            "status": status,
            "existing_files": existing_files,
            "missing_files": missing_files
        }
        
        report["summary"]["total_files"] += 3
        report["summary"]["existing_files"] += existing_files
        
        if status == "complete":
            report["summary"]["complete_games"] += 1
        elif status == "partial":
            report["summary"]["partial_games"] += 1
        else:
            report["summary"]["empty_games"] += 1
    
    with open("games_status_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("📋 Status report saved to games_status_report.json")
    
    # Print summary
    print(f"\n📊 Games Status Summary:")
    print(f"✅ Complete games: {report['summary']['complete_games']}")
    print(f"⚠️  Partial games: {report['summary']['partial_games']}")
    print(f"❌ Empty games: {report['summary']['empty_games']}")
    print(f"📁 Total files: {report['summary']['existing_files']}/{report['summary']['total_files']}")

def main():
    print("🎮 Complete Missing Images Tool")
    print("=" * 40)
    
    # Create status report first
    create_status_report()
    
    # Ask user what to do
    print("\nOptions:")
    print("1. Download placeholder images for missing files")
    print("2. Just show status report")
    
    choice = input("\nEnter your choice (1 or 2): ").strip()
    
    if choice == "1":
        complete_missing_images()
        print("\n🎉 Process completed!")
        print("📁 All games now have placeholder images")
        print("🌐 Use download_helper.html to replace with real images")
    else:
        print("✅ Status report generated only")

if __name__ == "__main__":
    main()
